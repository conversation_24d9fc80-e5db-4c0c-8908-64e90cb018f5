// DOM Elements
const chatInput = document.querySelector('.chat-input');
const sendButton = document.querySelector('.btn-send');
const micButton = document.querySelector('.btn-mic');
const attachButton = document.querySelector('.btn-attachment');
const navItems = document.querySelectorAll('.nav-item');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    focusInput();
});

// Initialize all event listeners
function initializeEventListeners() {
    // Send button click
    sendButton.addEventListener('click', handleSendMessage);
    
    // Enter key press in input
    chatInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSendMessage();
        }
    });
    
    // Input focus and blur events
    chatInput.addEventListener('focus', function() {
        this.parentElement.style.borderColor = '#6366f1';
    });
    
    chatInput.addEventListener('blur', function() {
        this.parentElement.style.borderColor = '#404040';
    });
    
    // Microphone button click
    micButton.addEventListener('click', handleMicClick);
    
    // Attachment button click
    attachButton.addEventListener('click', handleAttachmentClick);
    
    // Navigation item clicks
    navItems.forEach(item => {
        item.addEventListener('click', function() {
            handleNavClick(this);
        });
    });
    
    // Auto-resize input based on content
    chatInput.addEventListener('input', autoResizeInput);
}

// Handle sending a message
function handleSendMessage() {
    const message = chatInput.value.trim();
    
    if (message === '') {
        return;
    }
    
    // Here you would typically send the message to a backend
    console.log('Sending message:', message);
    
    // For demo purposes, just clear the input
    chatInput.value = '';
    
    // You could add the message to a chat area here
    // addMessageToChat(message, 'user');
    
    // Simulate a response (for demo)
    setTimeout(() => {
        // addMessageToChat('This is a demo response.', 'assistant');
        console.log('Received response: This is a demo response.');
    }, 1000);
}

// Handle microphone button click
function handleMicClick() {
    console.log('Microphone clicked');
    
    // Check if browser supports speech recognition
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
        startSpeechRecognition();
    } else {
        alert('Speech recognition is not supported in this browser.');
    }
}

// Start speech recognition
function startSpeechRecognition() {
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    const recognition = new SpeechRecognition();
    
    recognition.continuous = false;
    recognition.interimResults = false;
    recognition.lang = 'en-US';
    
    recognition.onstart = function() {
        micButton.style.color = '#ef4444';
        console.log('Speech recognition started');
    };
    
    recognition.onresult = function(event) {
        const transcript = event.results[0][0].transcript;
        chatInput.value = transcript;
        chatInput.focus();
    };
    
    recognition.onerror = function(event) {
        console.error('Speech recognition error:', event.error);
        micButton.style.color = '#8e8ea0';
    };
    
    recognition.onend = function() {
        micButton.style.color = '#8e8ea0';
        console.log('Speech recognition ended');
    };
    
    recognition.start();
}

// Handle attachment button click
function handleAttachmentClick() {
    console.log('Attachment clicked');
    
    // Create a file input element
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.multiple = true;
    fileInput.accept = 'image/*,text/*,.pdf,.doc,.docx';
    
    fileInput.onchange = function(e) {
        const files = Array.from(e.target.files);
        console.log('Files selected:', files);
        
        // Here you would handle file uploads
        files.forEach(file => {
            console.log('File:', file.name, file.type, file.size);
        });
    };
    
    fileInput.click();
}

// Handle navigation item clicks
function handleNavClick(clickedItem) {
    // Remove active class from all items
    navItems.forEach(item => {
        item.classList.remove('active');
    });
    
    // Add active class to clicked item
    clickedItem.classList.add('active');
    
    console.log('Navigation item clicked');
}

// Auto-resize input based on content
function autoResizeInput() {
    // Reset height to auto to get the correct scrollHeight
    chatInput.style.height = 'auto';
    
    // Set the height to the scrollHeight, but limit it to a maximum
    const maxHeight = 120; // Maximum height in pixels
    const newHeight = Math.min(chatInput.scrollHeight, maxHeight);
    
    chatInput.style.height = newHeight + 'px';
    
    // Enable/disable send button based on content
    if (chatInput.value.trim() === '') {
        sendButton.style.opacity = '0.5';
    } else {
        sendButton.style.opacity = '1';
    }
}

// Focus the input field
function focusInput() {
    chatInput.focus();
}

// Utility function to add messages to chat (for future implementation)
function addMessageToChat(message, sender) {
    // This would be implemented when adding a proper chat area
    console.log(`${sender}: ${message}`);
}

// Handle window resize
window.addEventListener('resize', function() {
    // Adjust layout if needed
    console.log('Window resized');
});

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + K to focus input
    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        focusInput();
    }
    
    // Escape to clear input
    if (e.key === 'Escape') {
        chatInput.value = '';
        chatInput.blur();
    }
});

// Initialize send button state
autoResizeInput();
